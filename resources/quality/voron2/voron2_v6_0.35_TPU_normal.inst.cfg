[general]
definition = voron2_base
name = Normal
version = 4

[metadata]
material = generic_tpu
quality_type = normal
setting_version = 25
type = quality
variant = V6 0.35mm

[values]
speed_infill = =speed_print
speed_layer_0 = =math.ceil(speed_print * 0.375)
speed_print = 180
speed_roofing = =speed_topbottom
speed_topbottom = =math.ceil(speed_print * 0.50)
speed_wall = =math.ceil(speed_print * 0.50)
speed_wall_0 = =speed_wall
speed_wall_x = =speed_print

