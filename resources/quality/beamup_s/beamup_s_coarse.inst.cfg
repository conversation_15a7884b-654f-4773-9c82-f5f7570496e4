[general]
definition = beamup_s
name = BeamUp S Coarse
version = 4

[metadata]
material = generic_pla
quality_type = coarse
setting_version = 25
type = quality
weight = -3

[values]
adhesion_type = brim
brim_line_count = 5
infill_before_walls = False
initial_layer_line_width_factor = 120.0
layer_height = 0.30
material_print_temperature = 215
material_print_temperature_layer_0 = 230
retraction_amount = 1.5
retraction_speed = 30
speed_infill = 50
speed_layer_0 = 25
speed_print = 50
speed_support_interface = 50
speed_topbottom = 50
speed_wall_0 = 35
speed_wall_x = 50
support_angle = 60
support_enable = True
support_infill_rate = 20
support_interface_enable = True
support_interface_height = 0.60
support_interface_pattern = zigzag
support_offset = 0.8
support_z_distance = 0.2
wall_thickness = 0.8
zig_zaggify_infill = True

