[general]
definition = ultimaker_s8
name = Normal
version = 4

[metadata]
material = generic_pva
quality_type = fast
setting_version = 25
type = quality
variant = BB 0.4
weight = -1

[values]
acceleration_prime_tower = 1500
acceleration_support = 1500
brim_replaces_support = False
build_volume_temperature = =70 if extruders_enabled_count > 1 and (not support_enable or extruder_nr != support_extruder_nr) else 35
cool_fan_enabled = =not (support_enable and (extruder_nr == support_infill_extruder_nr))
default_material_bed_temperature = =0 if extruders_enabled_count > 1 and (not support_enable or extruder_nr != support_extruder_nr) else 60
initial_layer_line_width_factor = 150
jerk_prime_tower = 4000
jerk_support = 4000
material_final_print_temperature = =material_print_temperature - 15
material_initial_print_temperature = =material_print_temperature - 10
minimum_support_area = 4
prime_tower_min_volume = 20
retraction_amount = 6.5
retraction_count_max = 5
skirt_brim_minimal_length = =min(2000, 175 / (layer_height * line_width))
speed_prime_tower = 50
speed_support = 50
speed_support_bottom = =2*speed_support_interface/5
speed_support_interface = 50
support_bottom_density = 70
support_infill_sparse_thickness = =2 * layer_height
support_interface_enable = True
support_z_distance = 0

