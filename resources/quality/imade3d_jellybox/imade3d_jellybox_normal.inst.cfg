[general]
definition = imade3d_jellybox
name = Medium
version = 4

[metadata]
global_quality = True
quality_type = normal
setting_version = 25
type = quality
weight = 0

[values]
adhesion_type = skirt
bottom_thickness = =top_bottom_thickness
bridge_enable_more_layers = False
bridge_settings_enabled = True
bridge_skin_material_flow = 85
bridge_skin_speed = 20
bridge_wall_material_flow = 85
bridge_wall_speed = 20
coasting_enable = True
coasting_min_volume = 2
coasting_volume = 0.032
cool_fan_speed_max = =cool_fan_speed
infill_before_walls = False
infill_enable_travel_optimization = True
infill_line_width = =round(line_width * 1.5, 2)
infill_pattern = zigzag
infill_sparse_density = 25
layer_height = 0.2
layer_height_0 = 0.3
line_width = =machine_nozzle_size
material_bed_temperature = =default_material_bed_temperature
material_bed_temperature_layer_0 = =material_bed_temperature + 5
material_print_temperature = =default_material_print_temperature
material_print_temperature_layer_0 = =material_print_temperature + 5
print_sequence = all_at_once
retract_at_layer_change = True
retraction_combing = noskin
retraction_combing_max_distance = 50
retraction_hop = 0.2
retraction_hop_enabled = True
retraction_min_travel = =machine_nozzle_size * 3
retraction_retract_speed = =retraction_speed
roofing_layer_count = 1
skin_line_width = =line_width * 1.2
skin_outline_count = 2
skirt_brim_minimal_length = 100
skirt_brim_speed = =speed_layer_0
skirt_gap = 5
skirt_line_count = 1
speed_layer_0 = 20
speed_roofing = 20
speed_topbottom = 25
speed_travel = =speed_print if magic_spiralize else 120
speed_travel_layer_0 = 60
support_angle = 60
support_bottom_enable = False
support_bottom_height = 0
support_connect_zigzags = False
support_enable = False
support_infill_rate = 20
support_interface_density = 70
support_interface_enable = True
support_interface_height = 2
support_interface_pattern = concentric
support_type = everywhere
support_use_towers = False
support_xy_distance = 0.8
support_xy_distance_overhang = =machine_nozzle_size / 2
support_z_distance = 0.2
travel_retract_before_outer_wall = True
wall_0_wipe_dist = =round(line_width * 1.2,1)

