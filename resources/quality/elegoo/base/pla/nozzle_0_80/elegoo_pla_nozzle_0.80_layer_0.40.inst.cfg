[general]
definition = elegoo_base
name = pla_noz0.80_lay0.40
version = 4

[metadata]
material = generic_pla
quality_type = Elegoo_layer_040
setting_version = 25
type = quality
variant = 0.80mm_Elegoo_Nozzle

[values]
gradual_infill_step_height = =3 * layer_height
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 15
prime_tower_enable = True
speed_infill = =math.ceil(speed_print * 35 / 45)
speed_print = 45
speed_topbottom = =math.ceil(speed_print * 35 / 45)
speed_wall = =math.ceil(speed_print * 35 / 45)
speed_wall_0 = =math.ceil(speed_wall * 35 / 40)
speed_wall_x = =speed_wall
support_angle = 70
top_bottom_thickness = =layer_height * 4

