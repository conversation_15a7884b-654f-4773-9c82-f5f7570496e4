[general]
definition = strateo3d
name = E
version = 4

[metadata]
material = emotiontech_abs
quality_type = e
setting_version = 25
type = quality
variant = Standard 1.0 Experimental
weight = 0

[values]
cool_fan_enabled = True
cool_fan_full_at_height = =layer_height_0 + 6 * layer_height
cool_fan_speed = 35
cool_fan_speed_max = 100
cool_min_layer_time = 11
cool_min_layer_time_fan_speed_max = 20
cool_min_speed = 10
layer_height_0 = =round(0.67*machine_nozzle_size, 2)
line_width = =machine_nozzle_size/machine_nozzle_size*1.0
material_flow = 97
material_print_temperature = =default_material_print_temperature + 5
material_print_temperature_layer_0 = =default_material_print_temperature + 3
prime_tower_enable = True
retraction_extra_prime_amount = 0.5
retraction_hop_only_when_collides = True
retraction_min_travel = =2*line_width
skin_overlap = 5
speed_layer_0 = =math.ceil(speed_print * 25/45)
speed_print = 45
speed_slowdown_layers = 2
speed_topbottom = =math.ceil(speed_print * 35/45)
speed_wall = =math.ceil(speed_print * 37/45)
speed_wall_0 = =math.ceil(speed_wall * 30/37)
support_angle = 45
support_bottom_distance = =layer_height
support_interface_density = 100
support_offset = 1
support_xy_distance = =line_width * 2
support_xy_distance_overhang = =wall_line_width_0
support_z_distance = =layer_height
wall_0_wipe_dist = =machine_nozzle_size/2
wall_line_width = =machine_nozzle_size/machine_nozzle_size*0.95
wall_line_width_x = =machine_nozzle_size/machine_nozzle_size*0.9

