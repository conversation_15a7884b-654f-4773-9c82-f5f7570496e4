[general]
definition = ultimaker3
name = Fine
version = 4

[metadata]
material = generic_abs
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.25
weight = 0

[values]
cool_fan_speed = 40
infill_overlap = =0 if infill_sparse_density > 80 else 15
material_print_temperature = =default_material_print_temperature - 20
retraction_prime_speed = 25
speed_topbottom = =math.ceil(speed_print * 30 / 55)

