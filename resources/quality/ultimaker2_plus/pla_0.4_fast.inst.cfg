[general]
definition = ultimaker2_plus
name = Normal
version = 4

[metadata]
material = generic_pla
quality_type = fast
setting_version = 25
type = quality
variant = 0.4 mm
weight = -1

[values]
cool_min_layer_time = 5
cool_min_speed = 10
infill_sparse_density = 18
speed_layer_0 = =round(speed_print * 30 / 60)
speed_print = 60
speed_topbottom = =math.ceil(speed_print * 30 / 60)
speed_travel = 150
speed_wall = =math.ceil(speed_print * 50 / 60)
top_bottom_thickness = 0.75

