[general]
definition = ultimaker2_plus
name = Normal
version = 4

[metadata]
material = generic_cpe
quality_type = fast
setting_version = 25
type = quality
variant = 0.4 mm
weight = -1

[values]
cool_fan_speed_min = =cool_fan_speed * 0.8
infill_sparse_density = 18
speed_infill = =math.ceil(speed_print * 45 / 45)
speed_print = 45
speed_topbottom = =math.ceil(speed_print * 30 / 45)
speed_travel = 150
speed_wall = =math.ceil(speed_print * 40 / 45)
speed_wall_0 = =math.ceil(speed_print * 30 / 45)
speed_wall_x = =math.ceil(speed_print * 40 / 45)
top_bottom_thickness = 0.75

