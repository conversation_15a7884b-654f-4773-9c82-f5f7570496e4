[general]
definition = ultimaker_methodxl
name = Fast
version = 4

[metadata]
material = ultimaker_nylon-cf_175
quality_type = draft
setting_version = 25
type = quality
variant = LABS
weight = -2

[values]
cool_fan_enabled = True
cool_fan_speed_max = 65
cool_min_layer_time = 3.5
cool_min_speed = 7
cool_min_temperature = =default_material_print_temperature-5
initial_layer_line_width_factor = 120
material_final_print_temperature = =default_material_print_temperature-5
material_initial_print_temperature = =default_material_print_temperature-5
raft_airgap = 0.18
raft_base_infill_overlap = 5
raft_base_line_spacing = =3 if extruder_nr == raft_interface_extruder_nr else 2
raft_base_line_width = =1.2 if extruder_nr == raft_interface_extruder_nr else 1
raft_base_thickness = =0.6 if extruder_nr == raft_interface_extruder_nr else 0.5
raft_interface_flow = 105
raft_interface_infill_overlap = 25
raft_interface_speed = =raft_speed * 7
raft_interface_z_offset = -0.08
raft_surface_flow = 105
raft_surface_infill_overlap = 35
raft_surface_line_spacing = 0.42
raft_surface_speed = =raft_speed * 7
raft_surface_thickness = 0.27
raft_surface_z_offset = -0.08
retraction_amount = 0.5
retraction_min_travel = 3.2
skin_overlap = 10
small_skin_width = 3.6
speed_prime_tower = =speed_print * 0.25
speed_print = 120.0
speed_roofing = =speed_print * 11/24
speed_topbottom = =speed_roofing
speed_wall_0 = =speed_print * 9/24
speed_wall_x = =speed_print * 13/24
support_angle = 50
support_bottom_density = 24
support_bottom_distance = =layer_height
support_bottom_enable = False
support_bottom_line_width = 0.6
support_bottom_stair_step_height = 0
support_infill_rate = 12.0
support_line_width = =line_width * 0.75
support_material_flow = 90
support_roof_density = 85
support_xy_distance = 0.3
support_z_distance = =layer_height

