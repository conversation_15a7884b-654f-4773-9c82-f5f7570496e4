[general]
definition = flsun_sr
name = Fine
version = 4

[metadata]
global_quality = True
quality_type = fine
setting_version = 25
type = quality
weight = 1

[values]
acceleration_enabled = False
adhesion_type = skirt
brim_width = 4.0
cool_fan_speed = 100
cool_lift_head = False
cool_min_layer_time = 4
infill_before_walls = False
infill_enable_travel_optimization = False
infill_pattern = grid
infill_support_angle = 50
ironing_enabled = False
ironing_only_highest_layer = True
jerk_enabled = False
jerk_travel = 25
jerk_travel_layer_0 = 20
jerk_wall_0 = 10
layer_height = 0.12
layer_height_0 = 0.2
material_bed_temperature_layer_0 = 60
material_final_print_temperature = 210
material_initial_print_temperature = 210
material_print_temperature = 210
material_print_temperature_layer_0 = 210
optimize_wall_printing_order = True
retract_at_layer_change = False
retraction_amount = 6.5
retraction_combing = noskin
retraction_combing_max_distance = 10
retraction_hop = 0.3
retraction_hop_enabled = True
retraction_hop_only_when_collides = True
retraction_speed = 40
skin_no_small_gaps_heuristic = True
skin_overlap = 10
skirt_line_count = 2
small_feature_speed_factor = 60
small_hole_max_size = 5
speed_infill = 100
speed_layer_0 = 25
speed_print = 80
speed_slowdown_layers = 5
speed_support = 80
speed_topbottom = 50
speed_travel = 120
speed_travel_layer_0 = 40
speed_z_hop = 50
support_angle = 60
support_brim_enable = True
support_brim_width = 4.0
support_enable = True
support_interface_density = 60
support_interface_enable = True
support_interface_height = 0.96
support_interface_pattern = grid
support_roof_density = 60
support_type = everywhere
support_wall_count = 1
support_xy_distance = 0.5
support_z_distance = 0.2
top_bottom_thickness = =layer_height*7
travel_avoid_other_parts = True
travel_avoid_supports = True
travel_retract_before_outer_wall = False
wall_thickness = =line_width*3
z_seam_corner = z_seam_corner_inner
z_seam_type = sharpest_corner

