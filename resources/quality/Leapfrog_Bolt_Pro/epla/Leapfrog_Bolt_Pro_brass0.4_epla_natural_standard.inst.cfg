[general]
definition = leapfrog_bolt_pro
name = Standard
version = 4

[metadata]
material = leapfrog_epla_natural
quality_type = standard
setting_version = 25
type = quality
variant = Brass 0.4
weight = 0

[values]
acceleration_enabled = False
adhesion_type = skirt
connect_infill_polygons = True
cool_fan_enabled = True
cool_fan_full_at_height = 0.5
cool_fan_full_layer = 4
cool_fan_speed = 100
cool_fan_speed_0 = 0
cool_fan_speed_max = 100
cool_fan_speed_min = 100
cool_min_layer_time = 5
cool_min_layer_time_fan_speed_max = 5
cool_min_speed = 5
infill_before_walls = True
infill_overlap = 0
infill_pattern = grid
infill_sparse_density = 20
infill_wipe_dist = 0
initial_layer_line_width_factor = 120
layer_height_0 = 0.3
line_width = 0.4
min_infill_area = 0
optimize_wall_printing_order = True
prime_tower_brim_enable = True
prime_tower_enable = True
prime_tower_min_volume = 6
prime_tower_size = 20
prime_tower_wipe_enabled = True
retract_at_layer_change = False
retraction_amount = 2
retraction_combing = all
retraction_enable = True
retraction_hop = 2
retraction_hop_after_extruder_switch = True
retraction_hop_after_extruder_switch_height = 2
retraction_hop_enabled = False
retraction_hop_only_when_collides = True
retraction_speed = 25
skin_outline_count = 1
skirt_brim_minimal_length = 250
skirt_gap = 1
skirt_line_count = 3
speed_layer_0 = 25
speed_print = 50
speed_slowdown_layers = 1
speed_support = 50
speed_topbottom = 25
speed_travel = 200
speed_travel_layer_0 = 45
speed_wall = 25
speed_wall_0 = 25
speed_wall_x = 40
support_angle = 50
support_bottom_stair_step_height = 0.3
support_bottom_stair_step_width = 5
support_connect_zigzags = False
support_infill_rate = 20
support_interface_enable = False
support_join_distance = 2
support_pattern = zigzag
support_tower_diameter = 3
support_tower_roof_angle = 65
support_xy_distance = 0.7
support_xy_distance_overhang = 0.4
support_z_distance = 0.3
switch_extruder_retraction_amount = 15
switch_extruder_retraction_speeds = 20
top_bottom_pattern = lines
top_bottom_thickness = 0.8
travel_avoid_other_parts = True
travel_avoid_supports = True
wall_0_wipe_dist = 0.2
wall_thickness = 0.8
z_seam_corner = hide_seam
z_seam_type = sharpest_corner

