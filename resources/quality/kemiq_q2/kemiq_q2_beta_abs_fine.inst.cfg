[general]
definition = kemiq_q2_beta
name = Fine
version = 4

[metadata]
material = generic_abs
quality_type = normal
setting_version = 25
type = quality
weight = 0

[values]
adhesion_type = raft
cool_fan_enabled = False
layer_height = 0.1
layer_height_0 = 0.3
material_bed_temperature = 85
material_print_temperature = 246
raft_airgap = 0.25
raft_base_line_width = 0.8
raft_base_thickness = 0.36
raft_interface_line_spacing = 2
raft_margin = 10
raft_speed = 20
raft_surface_layers = 3
raft_surface_line_spacing = 0.4
retraction_amount = 1.5
retraction_speed = 50
speed_infill = =math.ceil(speed_print * 60 / 50)
speed_layer_0 = =math.ceil(speed_print * 20 / 50)
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 30 / 50)
speed_travel = 120
speed_wall_0 = =math.ceil(speed_print * 25 / 50)
speed_wall_x = =math.ceil(speed_print * 45 / 50)

