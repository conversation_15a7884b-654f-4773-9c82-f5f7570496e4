[general]
definition = two_trees_base
name = Dynamic Quality
version = 4

[metadata]
material = generic_pla
quality_type = adaptive
setting_version = 25
type = quality
variant = 0.5mm Nozzle

[values]
cool_fan_enabled = True
material_bed_temperature = 60
material_final_print_temperature = =material_print_temperature
material_initial_print_temperature = =material_print_temperature
material_print_temperature = 190
material_print_temperature_layer_0 = 195
material_standby_temperature = =material_print_temperature
retraction_hop = 0.2
retraction_hop_only_when_collides = True

