[general]
definition = ultimaker_s5
name = Normal - Experimental
version = 4

[metadata]
is_experimental = True
material = ultimaker_pla
quality_type = fast
setting_version = 25
type = quality
variant = CC 0.4
weight = -1

[values]
gradual_infill_step_height = =3 * layer_height
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_print_temperature = =default_material_print_temperature + 10
speed_print = 45
speed_topbottom = =math.ceil(speed_print * 35 / 45)
speed_wall = =math.ceil(speed_print * 40 / 45)
speed_wall_0 = =math.ceil(speed_wall * 35 / 40)
speed_wall_x = =speed_wall
support_angle = 70
top_bottom_thickness = =layer_height * 4

