{"version": 2, "name": "Winbo Dragon(L)4", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON>", "manufacturer": "Winbo Smart Tech Co., Ltd.", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "winbo_dragonl4_extruder"}, "supports_usb_connection": false}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_layer_0": {"value": "acceleration_topbottom"}, "acceleration_prime_tower": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_print": {"value": "1800"}, "acceleration_support": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_support_interface": {"value": "acceleration_topbottom"}, "acceleration_topbottom": {"value": "math.ceil(acceleration_print * 500 / 4000)"}, "acceleration_travel": {"value": "2000"}, "acceleration_wall": {"value": "math.ceil(acceleration_print * 1000 / 4000)"}, "acceleration_wall_0": {"value": "math.ceil(acceleration_wall * 500 / 1000)"}, "brim_width": {"value": "4"}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_fan_speed": {"value": "100"}, "cool_fan_speed_max": {"value": "100"}, "cool_min_speed": {"value": "5"}, "default_material_print_temperature": {"value": "200"}, "fill_outline_gaps": {"value": "True"}, "gantry_height": {"value": "80"}, "gradual_infill_step_height": {"value": "1"}, "gradual_support_infill_step_height": {"value": "0.2"}, "gradual_support_infill_steps": {"value": "1 if support_structure != 'tree' else 0"}, "infill_overlap": {"value": "0"}, "initial_layer_line_width_factor": {"value": "120"}, "jerk_enabled": {"value": "True"}, "jerk_layer_0": {"value": "jerk_topbottom"}, "jerk_prime_tower": {"value": "math.ceil(jerk_print * 15 / 25)"}, "jerk_print": {"value": "25"}, "jerk_support": {"value": "math.ceil(jerk_print * 15 / 25)"}, "jerk_support_interface": {"value": "jerk_topbottom"}, "jerk_topbottom": {"value": "math.ceil(jerk_print * 5 / 25)"}, "jerk_travel": {"value": "25"}, "jerk_wall": {"value": "math.ceil(jerk_print * 10 / 25)"}, "jerk_wall_0": {"value": "math.ceil(jerk_wall * 5 / 10)"}, "line_width": {"value": "extruderValue(-1, 'machine_nozzle_size')"}, "machine_acceleration": {"default_value": 2000}, "machine_depth": {"default_value": 463}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\nG92 E2\nG1 E0 F200\nG28 X0 Y0\nM84 X Y E"}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-50, 90], [-50, -60], [50, -60], [50, 90]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 615}, "machine_max_feedrate_x": {"default_value": 300}, "machine_max_feedrate_y": {"default_value": 300}, "machine_max_feedrate_z": {"default_value": 40}, "machine_min_cool_heat_time_window": {"value": "15"}, "machine_name": {"default_value": "Winbo Dragon(L)4"}, "machine_nozzle_cool_down_speed": {"default_value": 0.8}, "machine_nozzle_heat_up_speed": {"default_value": 1.4}, "machine_start_gcode": {"default_value": "G21\nG90\nM82\nM107\nM9998\nG28 X0 Y0\nG28 Z0\nG1 F6000 Z0.3\nG92 E0\nG1 F800 X585 E12\nG92 E0"}, "machine_width": {"default_value": 615}, "material_bed_temp_wait": {"default_value": false}, "material_bed_temperature": {"maximum_value": "100"}, "material_bed_temperature_layer_0": {"maximum_value": "100"}, "material_print_temperature_layer_0": {"value": "material_print_temperature - 5"}, "min_infill_area": {"value": "1"}, "min_skin_width_for_expansion": {"value": "2"}, "prime_blob_enable": {"enabled": true}, "raft_airgap": {"value": "0"}, "raft_base_thickness": {"value": "0.3"}, "raft_interface_line_spacing": {"value": "0.5"}, "raft_interface_line_width": {"value": "0.5"}, "raft_interface_thickness": {"value": "0.2"}, "raft_jerk": {"value": "jerk_layer_0"}, "raft_margin": {"value": "5"}, "raft_surface_layers": {"value": "2"}, "retraction_amount": {"value": "4"}, "retraction_count_max": {"value": "10"}, "retraction_extrusion_window": {"value": "1"}, "retraction_hop": {"value": "0.5"}, "retraction_hop_enabled": {"value": "True"}, "retraction_hop_only_when_collides": {"value": "True"}, "retraction_min_travel": {"value": "5"}, "retraction_prime_speed": {"value": "25"}, "skin_overlap": {"value": "10"}, "speed_infill": {"value": "speed_print * line_width / infill_line_width"}, "speed_layer_0": {"value": "25"}, "speed_print": {"value": "70"}, "speed_support": {"value": "speed_print * line_width / support_line_width"}, "speed_support_interface": {"value": "speed_print * line_width / support_interface_line_width"}, "speed_topbottom": {"value": "speed_print * line_width / skin_line_width"}, "speed_travel": {"value": "100"}, "speed_wall": {"value": "speed_print * wall_line_width_0 / line_width"}, "speed_wall_0": {"value": "math.ceil(speed_wall * 0.6)"}, "speed_wall_x": {"value": "speed_wall"}, "support_angle": {"value": "50"}, "support_bottom_distance": {"value": "max(support_z_distance, layer_height * int(0.45 / layer_height))"}, "support_bottom_enable": {"value": "True"}, "support_bottom_height": {"value": "max((0.15 if(0.15 % layer_height == 0) else layer_height * int((0.15 + layer_height) / layer_height)), layer_height)"}, "support_bottom_pattern": {"value": "'zigzag'"}, "support_connect_zigzags": {"value": "False"}, "support_infill_rate": {"value": "8 if support_enable and support_structure == 'normal' else 0 if support_enable and support_structure == 'tree' else 8"}, "support_interface_density": {"value": "80"}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "0.5"}, "support_interface_line_width": {"value": "support_line_width"}, "support_line_width": {"value": "max(min(line_width, 0.4),line_width / 2)"}, "support_roof_pattern": {"value": "'concentric'"}, "support_xy_distance": {"value": "1"}, "support_z_distance": {"value": "max((0.2 if(0.2 % layer_height == 0) else layer_height * int((0.2 + layer_height) / layer_height)), layer_height)"}, "top_bottom_thickness": {"value": "max(1.2, layer_height * 6)"}, "travel_avoid_distance": {"value": "3"}, "wall_0_inset": {"value": "0.05"}, "wall_line_width_0": {"value": "line_width-0.05"}, "wall_line_width_x": {"value": "line_width"}, "wall_thickness": {"value": "2.4"}, "z_seam_type": {"value": "'shortest'"}}}