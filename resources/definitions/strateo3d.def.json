{"version": 2, "name": "DUAL600", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "eMotionTech", "manufacturer": "eMotionTech", "file_formats": "text/x-gcode", "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "strateo3d_right_extruder", "1": "strateo3d_left_extruder"}, "preferred_material": "emotiontech_pla", "preferred_quality_type": "c", "preferred_variant_name": "Standard 0.6", "variants_name": "Print Head"}, "overrides": {"acceleration_infill": {"maximum_value_warning": "1500"}, "acceleration_layer_0": {"maximum_value_warning": "1500", "value": "acceleration_topbottom"}, "acceleration_prime_tower": {"maximum_value_warning": "1500"}, "acceleration_print": {"maximum_value_warning": "1500", "value": "machine_acceleration"}, "acceleration_print_layer_0": {"maximum_value_warning": "1500"}, "acceleration_skirt_brim": {"maximum_value_warning": "1500"}, "acceleration_support": {"maximum_value_warning": "1500"}, "acceleration_support_bottom": {"maximum_value_warning": "1500"}, "acceleration_support_infill": {"maximum_value_warning": "1500"}, "acceleration_support_interface": {"maximum_value_warning": "1500", "value": "acceleration_topbottom"}, "acceleration_support_roof": {"maximum_value_warning": "1500"}, "acceleration_topbottom": {"maximum_value_warning": "1500", "value": "math.ceil(acceleration_print * 1250 / acceleration_print)"}, "acceleration_travel": {"maximum_value_warning": "1500", "value": "acceleration_print"}, "acceleration_travel_layer_0": {"maximum_value_warning": "1500"}, "acceleration_wall": {"maximum_value_warning": "1500", "value": "math.ceil(acceleration_print * 1250 / acceleration_print)"}, "acceleration_wall_0": {"maximum_value_warning": "1500", "value": "math.ceil(acceleration_print * 1000 / acceleration_print)"}, "acceleration_wall_x": {"maximum_value_warning": "1500"}, "adaptive_layer_height_variation_step": {"default_value": 0.05}, "adhesion_type": {"default_value": "skirt"}, "bottom_layers": {"value": "999999 if infill_sparse_density == 100 else math.ceil(round(((bottom_thickness-resolveOrValue('layer_height_0')) / resolveOrValue('layer_height'))+1, 4))"}, "bottom_thickness": {"value": "top_bottom_thickness-2 * layer_height+layer_height_0"}, "default_material_print_temperature": {"maximum_value": "415", "maximum_value_warning": "400"}, "expand_skins_expand_distance": {"value": "wall_line_width_0 + wall_line_count * wall_line_width_x"}, "extruder_prime_pos_abs": {"default_value": true}, "extruder_prime_pos_x": {"maximum_value": "machine_width", "minimum_value": "0"}, "extruder_prime_pos_y": {"maximum_value": "machine_depth", "minimum_value": "0"}, "gantry_height": {"value": "40"}, "gradual_infill_step_height": {"value": "layer_height * 10"}, "gradual_support_infill_step_height": {"value": "layer_height * 7"}, "infill_before_walls": {"default_value": false}, "infill_overlap": {"value": "0"}, "infill_wipe_dist": {"value": "0"}, "jerk_enabled": {"maximum_value_warning": "0.01", "value": "False"}, "jerk_infill": {"maximum_value_warning": "0.01", "value": "0.01"}, "jerk_layer_0": {"maximum_value_warning": "0.01", "value": "jerk_topbottom"}, "jerk_prime_tower": {"maximum_value_warning": "0.01", "value": "jerk_print * 15 / 25"}, "jerk_print": {"maximum_value_warning": "0.01", "value": "0.01"}, "jerk_print_layer_0": {"maximum_value_warning": "0.01"}, "jerk_skirt_brim": {"maximum_value_warning": "0.01"}, "jerk_support": {"maximum_value_warning": "0.01", "value": "jerk_print * 15 / 25"}, "jerk_support_bottom": {"maximum_value_warning": "0.01"}, "jerk_support_infill": {"maximum_value_warning": "0.01"}, "jerk_support_interface": {"maximum_value_warning": "0.01", "value": "jerk_topbottom"}, "jerk_support_roof": {"maximum_value_warning": "0.01"}, "jerk_topbottom": {"maximum_value_warning": "0.01", "value": "jerk_print * 5 / 25"}, "jerk_travel": {"maximum_value_warning": "0.01", "value": "machine_max_jerk_xy"}, "jerk_travel_layer_0": {"maximum_value_warning": "0.01"}, "jerk_wall": {"maximum_value_warning": "0.01", "value": "jerk_print * 5 / 25"}, "jerk_wall_0": {"maximum_value_warning": "0.01", "value": "jerk_wall * 5 / 10"}, "jerk_wall_x": {"maximum_value_warning": "0.01"}, "machine_acceleration": {"default_value": 1500}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 420}, "machine_end_gcode": {"default_value": "T1 \nM104 S0 \nT0 \nM104 S0 \nM140 S0 \nM141 S0 \nG91 \nG0 z1 \nG90 \nG28 \nM801.0 \nM84 \nM192"}, "machine_extruder_count": {"default_value": 2}, "machine_gcode_flavor": {"default_value": "<PERSON><PERSON>"}, "machine_head_with_fans_polygon": {"default_value": [[-76, -51.8], [25, -51.8], [25, 38.2], [-76, 38.2]]}, "machine_heat_zone_length": {"default_value": 7}, "machine_heated_bed": {"default_value": true}, "machine_heated_build_volume": {"default_value": true}, "machine_height": {"default_value": 495}, "machine_max_jerk_xy": {"default_value": 0.01}, "machine_max_jerk_z": {"default_value": 0}, "machine_min_cool_heat_time_window": {"value": "15"}, "machine_name": {"default_value": "DUAL600"}, "machine_nozzle_cool_down_speed": {"default_value": 0.5}, "machine_nozzle_heat_up_speed": {"default_value": 2.25}, "machine_start_gcode": {"default_value": ";M104 T0 S{material_standby_temperature, 0} \n;M104 T1 S{material_standby_temperature, 1} \n;M140 S{material_bed_temperature_layer_0} \n;M141 S{build_volume_temperature} \nG28 \nG90 \nG1 X0 Y0 Z15 F6000 \n;M190 S{material_bed_temperature_layer_0} \n;M109 S{material_print_temperature_layer_0, initial_extruder_nr} \nG1 Z0.3 \nG92 E0 \nG1 F300 X45 E18 \n;G1 F1500 E17 \nG1 F600 X25 \nG1 F600 Z3"}, "machine_width": {"default_value": 600}, "material_bed_temperature": {"maximum_value": "140", "maximum_value_warning": "140"}, "material_bed_temperature_layer_0": {"maximum_value": "140", "maximum_value_warning": "140"}, "material_final_print_temperature": {"maximum_value": "415", "maximum_value_warning": "400"}, "material_flow": {"default_value": 93}, "material_flow_layer_0": {"value": "math.ceil(material_flow * 1)"}, "material_initial_print_temperature": {"maximum_value": "415", "maximum_value_warning": "400"}, "material_print_temperature": {"maximum_value": "415", "maximum_value_warning": "400"}, "material_print_temperature_layer_0": {"maximum_value": "415", "maximum_value_warning": "400"}, "material_standby_temperature": {"maximum_value": "material_print_temperature", "maximum_value_warning": "material_print_temperature - 40"}, "meshfix_maximum_deviation": {"default_value": 0.04}, "optimize_wall_printing_order": {"value": "True"}, "prime_tower_min_volume": {"default_value": 35}, "retraction_amount": {"default_value": 1.5}, "retraction_combing_max_distance": {"default_value": 5}, "retraction_count_max": {"default_value": 15}, "retraction_hop": {"value": "2"}, "retraction_hop_enabled": {"value": "extruders_enabled_count > 1"}, "retraction_hop_only_when_collides": {"value": "True"}, "retraction_min_travel": {"value": "3 * line_width"}, "retraction_prime_speed": {"value": "retraction_speed-10"}, "skin_overlap": {"value": "10"}, "skirt_brim_minimal_length": {"default_value": 333}, "speed_layer_0": {"value": "20"}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": "50"}, "speed_support": {"value": "speed_wall"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "math.ceil(speed_print * 20/35)"}, "speed_travel": {"value": "150"}, "speed_travel_layer_0": {"value": "100"}, "speed_wall": {"value": "math.ceil(speed_print * 3/4)"}, "speed_wall_0": {"value": "math.ceil(speed_wall * 2/3)"}, "speed_wall_x": {"value": "speed_wall"}, "support_bottom_distance": {"maximum_value_warning": "machine_nozzle_size * 1.5", "value": "extruderValue(support_bottom_extruder_nr if support_bottom_enable else support_infill_extruder_nr, 'support_z_distance')"}, "support_interface_enable": {"default_value": true}, "support_interface_height": {"value": "layer_height * 3"}, "support_interface_offset": {"value": "support_offset"}, "support_top_distance": {"maximum_value_warning": "machine_nozzle_size * 1.5"}, "support_xy_distance": {"value": "line_width * 1.7"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_z_distance": {"maximum_value_warning": "machine_nozzle_size * 1.5", "value": "layer_height*2"}, "switch_extruder_prime_speed": {"value": "retraction_prime_speed"}, "switch_extruder_retraction_amount": {"value": "7"}, "switch_extruder_retraction_speeds": {"value": "retraction_retract_speed"}, "top_bottom_thickness": {"minimum_value_warning": "layer_height * 2", "value": "3 * layer_height"}, "travel_avoid_distance": {"value": "3 if extruders_enabled_count > 1 else machine_nozzle_tip_outer_diameter / 2 * 1.5"}, "wall_thickness": {"value": "wall_line_width_0 + wall_line_width_x"}}}