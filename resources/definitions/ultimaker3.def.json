{"version": 2, "name": "Ultimaker 3", "inherits": "ultimaker", "metadata": {"visible": true, "author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "application/gzip;text/x-gcode", "platform": "ultimaker3_platform.obj", "bom_numbers": [9066], "exclude_materials": ["generic_cffcpe", "generic_cffpa", "generic_flexible", "generic_gffcpe", "generic_gffpa", "generic_hips", "generic_petcf", "structur3d_", "ultimaker_petcf", "generic_cffpps", "ultimaker_ppscf"], "firmware_update_info": {"check_urls": ["https://software.ultimaker.com/releases/firmware/9066/stable/um-update.swu.version"], "id": 9066, "update_url": "https://ultimaker.com/firmware?utm_source=cura&utm_medium=software&utm_campaign=fw-update"}, "first_start_actions": ["DiscoverUM3Action"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker3_extruder_left", "1": "ultimaker3_extruder_right"}, "platform_offset": [0, 0, 0], "platform_texture": "Ultimaker3backplate.png", "preferred_quality_type": "fast", "preferred_variant_name": "AA 0.4", "supported_actions": ["DiscoverUM3Action"], "supports_network_connection": true, "supports_usb_connection": false, "variants_name": "Print Core", "variants_name_has_translation": true}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_prime_tower": {"value": "math.ceil(acceleration_print * 2000 / 3500)"}, "acceleration_print": {"value": "3500"}, "acceleration_support": {"value": "math.ceil(acceleration_print * 2000 / 3500)"}, "acceleration_support_interface": {"value": "acceleration_topbottom"}, "acceleration_topbottom": {"value": "math.ceil(acceleration_print * 1000 / 3500)"}, "acceleration_wall": {"value": "math.ceil(acceleration_print * 1500 / 3500)"}, "acceleration_wall_0": {"value": "math.ceil(acceleration_wall * 1000 / 3500)"}, "brim_width": {"value": "3"}, "cool_fan_speed": {"value": "50"}, "default_material_print_temperature": {"value": "200"}, "extruder_prime_pos_abs": {"default_value": true}, "gantry_height": {"value": "60"}, "infill_overlap": {"value": "0"}, "infill_pattern": {"value": "'zigzag' if infill_sparse_density > 80 else 'triangles'"}, "infill_wipe_dist": {"value": "0"}, "jerk_enabled": {"value": "True"}, "machine_acceleration": {"default_value": 3000}, "machine_depth": {"default_value": 215}, "machine_disallowed_areas": {"default_value": [[[92.8, -53.4], [92.8, -97.5], [116.5, -97.5], [116.5, -53.4]], [[73.8, 107.5], [73.8, 100.5], [116.5, 100.5], [116.5, 107.5]], [[74.6, 107.5], [74.6, 100.5], [116.5, 100.5], [116.5, 107.5]], [[74.9, -97.5], [74.9, -107.5], [116.5, -107.5], [116.5, -97.5]], [[-116.5, -103.5], [-116.5, -107.5], [-100.9, -107.5], [-100.9, -103.5]], [[-116.5, 105.8], [-96.9, 105.8], [-96.9, 107.5], [-116.5, 107.5]]]}, "machine_end_gcode": {"default_value": "G91 ;Relative movement\nG0 F15000 X8.0 Z0.5 E-4.5 ;Wiping+material retraction\nG0 F10000 Z1.5 E4.5 ;Compensation for the retraction\nG90 ;Disable relative movement"}, "machine_extruder_count": {"default_value": 2}, "machine_gcode_flavor": {"default_value": "<PERSON>"}, "machine_head_with_fans_polygon": {"default_value": [[-41.9, -45.8], [-41.9, 33.9], [59.9, 33.9], [59.9, -45.8]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 200}, "machine_max_feedrate_x": {"default_value": 300}, "machine_max_feedrate_y": {"default_value": 300}, "machine_max_feedrate_z": {"default_value": 40}, "machine_min_cool_heat_time_window": {"value": "15"}, "machine_name": {"default_value": "Ultimaker 3"}, "machine_nozzle_cool_down_speed": {"default_value": 0.8}, "machine_nozzle_heat_up_speed": {"default_value": 1.4}, "machine_start_gcode": {"default_value": ""}, "machine_width": {"default_value": 233}, "material_bed_temperature": {"maximum_value": "115"}, "material_bed_temperature_layer_0": {"maximum_value": "115"}, "multiple_mesh_overlap": {"value": "0"}, "optimize_wall_printing_order": {"value": "True"}, "prime_blob_enable": {"default_value": true, "enabled": true, "value": "resolveOrValue('print_sequence') != 'one_at_a_time'"}, "prime_tower_enable": {"default_value": true}, "prime_tower_position_x": {"value": "185"}, "prime_tower_wipe_enabled": {"default_value": false}, "retraction_amount": {"value": "6.5"}, "retraction_hop": {"value": "2"}, "retraction_hop_enabled": {"value": "extruders_enabled_count > 1"}, "retraction_hop_only_when_collides": {"value": "True"}, "retraction_prime_speed": {"value": "15"}, "skin_overlap": {"value": "10"}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": "35"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "math.ceil(speed_print * 20 / 35)"}, "speed_travel": {"value": "250"}, "speed_wall": {"value": "math.ceil(speed_print * 30 / 35)"}, "speed_wall_0": {"value": "math.ceil(speed_wall * 20 / 30)"}, "speed_wall_x": {"value": "speed_wall"}, "support_angle": {"value": "45"}, "switch_extruder_prime_speed": {"value": "15"}, "switch_extruder_retraction_amount": {"value": "8"}, "top_bottom_thickness": {"value": "1"}, "travel_avoid_distance": {"value": "3 if extruders_enabled_count > 1 else machine_nozzle_tip_outer_diameter / 2 * 1.5"}, "wall_0_inset": {"value": "0"}, "zig_zaggify_infill": {"value": "gradual_infill_steps == 0"}}}