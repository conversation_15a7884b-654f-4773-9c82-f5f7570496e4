[general]
definition = elegoo_neptune_4
name = Accurate
version = 4

[metadata]
intent_category = engineering
is_experimental = True
material = generic_pla
quality_type = Elegoo_layer_020
setting_version = 25
type = intent
variant = 0.40mm_Elegoo_Nozzle

[values]
speed_infill = =speed_print
speed_print = 150
speed_topbottom = =speed_print * 2 / 3
speed_travel = =min(speed_print * 4 / 3, 250) if speed_print <= 250 else speed_print
speed_wall = =speed_print * 2 / 3
speed_wall_0 = =speed_wall
speed_wall_x = =speed_wall
top_bottom_thickness = =wall_thickness
wall_thickness = =line_width * 3

